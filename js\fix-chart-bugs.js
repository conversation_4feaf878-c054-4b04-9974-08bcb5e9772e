// 图表代码修复脚本
console.log("修复图表代码中的错误...");

// 修复自耦变压器温度监测图表的问题
document.addEventListener('DOMContentLoaded', function() {
    // 检查初始化函数中是否有缺少return的if语句
    const fixInitAutotransformerTemp = function() {
        if (typeof initAutotransformerTemp === 'function') {
            console.log("正在修复自耦变压器温度监测图表...");
            // 重新定义函数
            window._originalInitAutotransformerTemp = initAutotransformerTemp;
            window.initAutotransformerTemp = function() {
                const chartDom = document.getElementById('autotransformer-temp');
                if (!chartDom) return;  // 确保正确返回
                
                window.autotransformerTempChart = echarts.init(chartDom);
                
                // 生成24小时的温度数据
                const times = [];
                const at1TempData = [];
                const at2TempData = [];
                const at3TempData = [];
                const at4TempData = [];
                
                for (let i = 0; i < 24; i++) {
                    times.push(i + ':00');
                    
                    // 模拟温度波动 - 白天温度较高
                    const timeEffect = (i >= 10 && i <= 18) ? 1.1 : 0.9;
                    
                    // 基础温度值 + 随机波动
                    at1TempData.push((55 + Math.random() * 4) * timeEffect);
                    at2TempData.push((57 + Math.random() * 4) * timeEffect);
                    at3TempData.push((54 + Math.random() * 4) * timeEffect);
                    at4TempData.push((56 + Math.random() * 4) * timeEffect);
                }
                
                const option = {
                    title: {
                        text: '自耦变压器温度监测',
                        left: 'center',
                        textStyle: {
                            fontSize: 14,
                            color: '#a0b4e6'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].name + '<br/>';
                            params.forEach(param => {
                                result += param.marker + ' ' + param.seriesName + ': ' + 
                                         param.value.toFixed(1) + ' ℃<br/>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['1AT温度', '2AT温度', '3AT温度', '4AT温度'],
                        bottom: 0,
                        textStyle: {
                            color: '#a0b4e6'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: times,
                        axisLine: {
                            lineStyle: {
                                color: '#4c5c84'
                            }
                        },
                        axisLabel: {
                            color: '#a0b4e6'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '温度(℃)',
                        min: 'dataMin',
                        max: 'dataMax',
                        splitLine: {
                            lineStyle: {
                                color: '#26355c'
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#4c5c84'
                            }
                        },
                        axisLabel: {
                            color: '#a0b4e6'
                        }
                    },
                    series: [
                        {
                            name: '1AT温度',
                            type: 'line',
                            data: at1TempData,
                            smooth: true,
                            lineStyle: {
                                width: 2,
                                color: '#36cfc9'
                            }
                        },
                        {
                            name: '2AT温度',
                            type: 'line',
                            data: at2TempData,
                            smooth: true,
                            lineStyle: {
                                width: 2,
                                color: '#40a9ff'
                            }
                        },
                        {
                            name: '3AT温度',
                            type: 'line',
                            data: at3TempData,
                            smooth: true,
                            lineStyle: {
                                width: 2,
                                color: '#9254de'
                            }
                        },
                        {
                            name: '4AT温度',
                            type: 'line',
                            data: at4TempData,
                            smooth: true,
                            lineStyle: {
                                width: 2,
                                color: '#f759ab'
                            }
                        }
                    ]
                };
                
                window.autotransformerTempChart.setOption(option);
                
                // 更新指标数据
                updateAutotransformerTempMetrics();
                
                // 添加响应式调整
                window.addEventListener('resize', function() {
                    if (window.autotransformerTempChart) {
                        window.autotransformerTempChart.resize();
                    }
                });
            };
        }
    };
    
    // 修复能耗异常分析图表的问题
    const fixInitEnergyAnomaly = function() {
        if (typeof initEnergyAnomaly === 'function') {
            console.log("正在修复能耗异常分析图表...");
            // 重新定义函数
            window._originalInitEnergyAnomaly = initEnergyAnomaly;
            window.initEnergyAnomaly = function() {
                const chartDom = document.getElementById('energy-anomaly');
                if (!chartDom) return;
                
                const chart = echarts.init(chartDom);
                
                // 模拟最近30天的数据
                const days = [];
                const now = new Date();
                for (let i = 29; i >= 0; i--) {
                    const day = new Date(now);
                    day.setDate(now.getDate() - i);
                    days.push(day.getDate() + '日');
                }
                
                // 模拟正常能耗数据
                const normalData = days.map(() => Math.round(Math.random() * 30 + 320));
                
                // 模拟实际能耗数据 (包括一些异常)
                const actualData = normalData.map((value, index) => {
                    // 添加一些异常点
                    if (index === 8 || index === 22) {
                        return value * 1.3; // 明显高于预期
                    } else if (index === 15) {
                        return value * 0.7; // 明显低于预期
                    } else {
                        return value + Math.random() * 30 - 15; // 正常波动
                    }
                });
                
                const option = {
                    title: {
                        text: '能耗异常分析',
                        left: 'center',
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const date = params[0].axisValue;
                            let result = date + '<br/>';
                            
                            params.forEach(param => {
                                result += param.marker + ' ' + param.seriesName + ': ' + param.value + ' kWh<br/>';
                            });
                            
                            // 计算偏差
                            const expected = params[0].value;
                            const actual = params[1].value;
                            const deviation = ((actual - expected) / expected * 100).toFixed(1);
                            
                            result += '<br/>偏差: ' + deviation + '%';
                            
                            // 添加异常提示
                            if (Math.abs(deviation) > 15) {
                                result += '<br/><span style="color: #f44336; font-weight: bold">⚠️ 异常数据</span>';
                            }
                            
                            return result;
                        }
                    },
                    legend: {
                        data: ['预期能耗', '实际能耗'],
                        bottom: 10
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: days,
                        axisLine: {
                            lineStyle: {
                                color: '#4c5c84'
                            }
                        },
                        axisLabel: {
                            interval: 2,
                            color: '#666'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '能耗 (kWh)',
                        axisLine: {
                            lineStyle: {
                                color: '#4c5c84'
                            }
                        },
                        axisLabel: {
                            color: '#666'
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                color: '#ddd'
                            }
                        }
                    },
                    series: [
                        {
                            name: '预期能耗',
                            type: 'line',
                            data: normalData,
                            smooth: true,
                            lineStyle: {
                                color: '#1a7ced',
                                width: 2
                            },
                            itemStyle: {
                                color: '#1a7ced'
                            },
                            symbol: 'circle',
                            symbolSize: 6
                        },
                        {
                            name: '实际能耗',
                            type: 'line',
                            data: actualData,
                            smooth: true,
                            lineStyle: {
                                color: '#ff7037',
                                width: 3
                            },
                            itemStyle: {
                                color: '#ff7037'
                            },
                            symbol: 'circle',
                            symbolSize: 8,
                            markPoint: {
                                data: [
                                    // 标记异常点
                                    { coord: [8, actualData[8]], value: '异常', symbolSize: 60 },
                                    { coord: [15, actualData[15]], value: '异常', symbolSize: 60 },
                                    { coord: [22, actualData[22]], value: '异常', symbolSize: 60 }
                                ],
                                symbol: 'pin',
                                itemStyle: {
                                    color: '#f44336'
                                },
                                label: {
                                    color: '#fff'
                                }
                            }
                        }
                    ]
                };
                
                const energyAnomalyChart = echarts.init(chartDom);
                energyAnomalyChart.setOption(option);
                window.energyAnomalyChart = energyAnomalyChart;
                
                // 自适应窗口大小
                window.addEventListener('resize', function() {
                    if (window.energyAnomalyChart) {
                        window.energyAnomalyChart.resize();
                    }
                });
            };
        }
    };
    
    // 应用修复
    setTimeout(function() {
        fixInitAutotransformerTemp();
        fixInitEnergyAnomaly();
        
        // 重新初始化变压器监控页面图表
        if (document.querySelector('#transformer.page.active')) {
            console.log("重新初始化变压器监控页面图表...");
            initTransformerPower();
            initTransformerTemp();
            initAutotransformerTemp();
            initTransformerCurrent();
        }
        
        // 重新初始化能耗分析页面图表
        if (document.querySelector('#analysis.page.active')) {
            console.log("重新初始化能耗分析页面图表...");
            initEnergyTrend();
            initLoadDistribution();
            initPowerFactorAnalysis();
            initEnergyAnomaly();
        }
    }, 500);
});
