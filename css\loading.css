/* 图表加载动画样式 */
.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(6, 10, 33, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.chart-loading.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(93, 144, 255, 0.3);
    border-radius: 50%;
    border-top-color: #5d90ff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 图表显示完成效果 */
.chart-loaded {
    animation: chartFadeIn 0.5s ease-in-out;
}

@keyframes chartFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据更新指示器 */
.data-update-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(93, 144, 255, 0.2);
    color: #5d90ff;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    z-index: 5;
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.data-update-indicator.active {
    opacity: 1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(93, 144, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(93, 144, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(93, 144, 255, 0);
    }
}
