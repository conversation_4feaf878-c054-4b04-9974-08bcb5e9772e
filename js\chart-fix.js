// 图表显示修复脚本
document.addEventListener('DOMContentLoaded', function() {
    // 显示所有图表的加载动画
    showAllChartLoadingIndicators();
    
    // 确保在页面加载完成后延迟执行图表初始化，给DOM足够的渲染时间
    setTimeout(function() {
        initAllCharts();
        console.log('Charts initialized with delay for better rendering');
        
        // 延迟隐藏加载动画，给图表渲染一些时间
        setTimeout(function() {
            hideAllChartLoadingIndicators();
        }, 500);
    }, 300);

    // 为所有图表容器添加resizeObserver以便在容器大小变化时调整图表尺寸
    setupChartResizeObservers();
});

// 显示所有图表的加载动画
function showAllChartLoadingIndicators() {
    const loadingIndicators = document.querySelectorAll('.chart-loading');
    loadingIndicators.forEach(indicator => {
        indicator.classList.remove('hidden');
    });
}

// 隐藏所有图表的加载动画
function hideAllChartLoadingIndicators() {
    const loadingIndicators = document.querySelectorAll('.chart-loading');
    loadingIndicators.forEach(indicator => {
        indicator.classList.add('hidden');
        
        // 添加图表加载完成的动画类
        const chartContainer = indicator.parentElement;
        chartContainer.classList.add('chart-loaded');
    });
}

// 显示特定图表的加载动画
function showChartLoadingIndicator(chartId) {
    const chartContainer = document.getElementById(chartId);
    if (!chartContainer) return;
    
    const loadingIndicator = chartContainer.querySelector('.chart-loading');
    if (loadingIndicator) {
        loadingIndicator.classList.remove('hidden');
    }
}

// 隐藏特定图表的加载动画
function hideChartLoadingIndicator(chartId) {
    const chartContainer = document.getElementById(chartId);
    if (!chartContainer) return;
    
    const loadingIndicator = chartContainer.querySelector('.chart-loading');
    if (loadingIndicator) {
        loadingIndicator.classList.add('hidden');
        chartContainer.classList.add('chart-loaded');
    }
}

// 显示数据更新指示器
function showDataUpdateIndicator(chartId) {
    const chartContainer = document.getElementById(chartId);
    if (!chartContainer) return;
    
    const updateIndicator = chartContainer.querySelector('.data-update-indicator');
    if (updateIndicator) {
        updateIndicator.classList.add('active');
        
        // 3秒后自动隐藏更新指示器
        setTimeout(() => {
            updateIndicator.classList.remove('active');
        }, 3000);
    }
}

// 监听所有图表容器的大小变化
function setupChartResizeObservers() {
    const chartContainers = document.querySelectorAll('.chart');
    
    if ('ResizeObserver' in window) {
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                const chartId = entry.target.id;
                if (chartId && window[chartId + 'Chart']) {
                    window[chartId + 'Chart'].resize();
                    console.log(`Resized chart: ${chartId}`);
                }
            }
        });
        
        chartContainers.forEach(container => {
            resizeObserver.observe(container);
        });
    }
}

// 重新初始化特定页面的图表
function reinitializePageCharts(pageId) {
    console.log(`Reinitializing charts for page: ${pageId}`);
    
    // 显示该页面所有图表的加载动画
    const chartContainers = document.querySelectorAll(`#${pageId} .chart`);
    chartContainers.forEach(container => {
        const loadingIndicator = container.querySelector('.chart-loading');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
    });
    
    // 延迟一点时间再初始化图表，使加载动画可以显示
    setTimeout(() => {        switch(pageId) {
            case 'overview':
                initPowerChart();
                initPowerFactorGauge();
                initEnergyDistribution();
                initEnergyConsumption();
                break;
            case 'transformer':
                initTransformerPower();
                initTransformerTemp();
                initAutotransformerTemp();
                initTransformerCurrent();
                break;
            case 'feeder':
                initFeederCurrent();
                initFeederVoltage();
                initFeederComparison();
                initFeederImpedance();
                break;
            case 'analysis':
                initEnergyTrend();
                initLoadDistribution();
                initPowerFactorAnalysis();
                initEnergyAnomaly();
                break;
        }
        
        // 初始化完成后，隐藏加载动画
        setTimeout(() => {
            chartContainers.forEach(container => {
                const loadingIndicator = container.querySelector('.chart-loading');
                if (loadingIndicator) {
                    loadingIndicator.classList.add('hidden');
                }
                container.classList.add('chart-loaded');
            });
        }, 500);
    }, 100);
}

// 修复图表显示问题的函数 - 在页面切换后调用
function fixChartDisplay(pageId) {
    const chartContainers = document.querySelectorAll(`#${pageId} .chart`);
    
    // 显示所有图表的加载动画
    chartContainers.forEach(container => {
        const loadingIndicator = container.querySelector('.chart-loading');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
        
        // 确保所有图表容器都有显式的高度
        if (getComputedStyle(container).height === '0px') {
            container.style.height = '300px';
        }
    });
    
    // 为了解决图表渲染问题，我们延迟重新初始化该页面的图表
    setTimeout(() => {
        reinitializePageCharts(pageId);
        
        // 触发窗口调整事件，确保图表正确渲染
        window.dispatchEvent(new Event('resize'));
    }, 100);
}
