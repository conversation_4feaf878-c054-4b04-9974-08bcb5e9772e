// 遥测数据处理与集成
(function() {
    // 设备和遥测点映射
    const DEVICE_TELEMETRY_MAP = {
        // 变压器数据映射
        "transformers": {            "1B_牵引变压器": {
                "frequency": "频率",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因素",
                "loadRate": "负载率"
            },
            "2B_牵引变压器": {
                "frequency": "频率",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因素",
                "loadRate": "负载率"
            },
            "动力变压器": {
                "frequency": "频率",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因素",
                "loadRate": "负载率",
                "temperature": "油温"
            },
            "1号自耦变压器": {
                "temperature": "1AT温度"
            },
            "2号自耦变压器": {
                "temperature": "2AT温度"
            },
            "3号自耦变压器": {
                "temperature": "3AT温度"
            },
            "4号自耦变压器": {
                "temperature": "4AT温度"
            }
        },
        
        // 保护装置数据映射
        "protectionDevices": {
            "1号主变保护装置": {
                "highVoltageAB": "高压侧AB线电压",
                "highVoltageBC": "高压侧BC线电压",
                "highVoltageCA": "高压侧CA线电压",
                "highCurrentA": "高压侧A相电流",
                "highCurrentB": "高压侧B相电流",
                "highCurrentC": "高压侧C相电流",
                "highCurrentAngleA": "高压侧A相电流角度",
                "highCurrentAngleB": "高压侧B相电流角度",
                "highCurrentAngleC": "高压侧C相电流角度"
            },
            "1号主变测控装置": {
                "oilTemp1": "主变油温1",
                "copperTemp1": "主变铜温1",
                "oilTemp2": "主变油温2",
                "copperTemp2": "主变铜温2",
                "workMode": "工作方式",
                "powerFactorAngleA": "A相功率因素角",
                "powerFactorAngleB": "B相功率因素角",
                "powerFactorAngleC": "C相功率因素角"
            }
        },
        
        // 馈线数据映射
        "feeders": {
            "212馈线保护测控装置": {
                "voltage": "馈线电压",
                "current": "馈线电流",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因数"
            },
            "212B馈线保护测控装置": {
                "voltage": "馈线电压",
                "current": "馈线电流",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因数"
            },
            "214馈线保护测控装置": {
                "voltage": "馈线电压",
                "current": "馈线电流",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因数"
            },
            "215馈线保护测控装置": {
                "voltage": "馈线电压",
                "current": "馈线电流",
                "activePower": "有功功率",
                "reactivePower": "无功功率",
                "powerFactor": "功率因数"
            }
        }
    };
    
    // 模拟数据生成 - 正常运行范围内生成随机数据
    function generateRandomInRange(min, max, precision = 2) {
        const randomValue = Math.random() * (max - min) + min;
        return parseFloat(randomValue.toFixed(precision));
    }
    
    // 根据设备ID获取对应的遥测数据
    window.getTelemetryData = function(deviceType, deviceId, telemetryPoint) {
        // 检查设备类型是否存在
        if (!DEVICE_TELEMETRY_MAP[deviceType]) {
            console.error(`未知设备类型: ${deviceType}`);
            return null;
        }
        
        // 检查设备ID是否存在
        if (!DEVICE_TELEMETRY_MAP[deviceType][deviceId]) {
            console.error(`未知设备ID: ${deviceId}`);
            return null;
        }
        
        // 检查遥测点是否存在
        if (!DEVICE_TELEMETRY_MAP[deviceType][deviceId][telemetryPoint]) {
            console.error(`未知遥测点: ${telemetryPoint}`);
            return null;
        }
        
        // 获取遥测点的中文名称
        const telemetryName = DEVICE_TELEMETRY_MAP[deviceType][deviceId][telemetryPoint];
        
        // 根据不同的遥测点生成不同范围的随机数据
        switch (telemetryPoint) {
            case 'frequency':
                return generateRandomInRange(49.8, 50.2, 2);
            case 'activePower':
                return generateRandomInRange(200, 400, 1);
            case 'reactivePower':
                return generateRandomInRange(100, 250, 1);
            case 'powerFactor':
                return generateRandomInRange(0.85, 0.98, 2);
            case 'temperature':
                return generateRandomInRange(35, 65, 1);
            case 'oilTemp1':
            case 'oilTemp2':
                return generateRandomInRange(40, 75, 1);
            case 'copperTemp1':
            case 'copperTemp2':
                return generateRandomInRange(50, 85, 1);
            case 'highVoltageAB':
            case 'highVoltageBC':
            case 'highVoltageCA':
                return generateRandomInRange(110, 121, 1);
            case 'highCurrentA':
            case 'highCurrentB':
            case 'highCurrentC':
                return generateRandomInRange(80, 150, 1);
            case 'voltage':
                return generateRandomInRange(26.5, 28.5, 1);
            case 'current':
                return generateRandomInRange(100, 350, 0);
            default:
                return generateRandomInRange(0, 100, 1);
        }
    };
    
    // 获取设备历史数据（模拟）
    window.getTelemetryHistoryData = function(deviceType, deviceId, telemetryPoint, timeRange = 'day', pointCount = 24) {
        const data = {
            timestamps: [],
            values: []
        };
        
        const now = new Date();
        let interval;
        
        // 根据时间范围设置数据点间隔
        switch(timeRange) {
            case 'day':
                interval = 3600 * 1000; // 1小时
                break;
            case 'week':
                interval = 6 * 3600 * 1000; // 6小时
                break;
            case 'month':
                interval = 24 * 3600 * 1000; // 1天
                break;
            case 'year':
                interval = 30 * 24 * 3600 * 1000; // 30天
                break;
            default:
                interval = 3600 * 1000; // 默认1小时
        }
        
        // 生成历史数据点
        for (let i = pointCount - 1; i >= 0; i--) {
            const timestamp = new Date(now.getTime() - i * interval);
            data.timestamps.push(formatTime(timestamp));
            
            // 根据遥测点类型生成相应范围的数据
            let value;
            switch (telemetryPoint) {
                case 'frequency':
                    value = generateRandomInRange(49.8, 50.2, 2);
                    break;
                case 'activePower':
                    // 模拟日负荷曲线，早晚高峰
                    const hour = timestamp.getHours();
                    if (hour >= 7 && hour <= 9) { // 早高峰
                        value = generateRandomInRange(320, 400, 1);
                    } else if (hour >= 17 && hour <= 19) { // 晚高峰
                        value = generateRandomInRange(350, 420, 1);
                    } else if (hour >= 23 || hour <= 5) { // 夜间低谷
                        value = generateRandomInRange(150, 250, 1);
                    } else { // 其他时段
                        value = generateRandomInRange(200, 300, 1);
                    }
                    break;
                case 'temperature':
                    // 根据一天的温度变化趋势
                    const tempHour = timestamp.getHours();
                    if (tempHour >= 12 && tempHour <= 15) {
                        value = generateRandomInRange(55, 65, 1); // 最热时段
                    } else if (tempHour >= 0 && tempHour <= 5) {
                        value = generateRandomInRange(35, 45, 1); // 最冷时段
                    } else {
                        value = generateRandomInRange(45, 55, 1); // 其他时段
                    }
                    break;
                default:
                    value = window.getTelemetryData(deviceType, deviceId, telemetryPoint);
            }
            
            data.values.push(value);
        }
        
        return data;
    };
    
    // 格式化时间为 HH:MM 或 MM-DD 格式
    function formatTime(date, format = 'HH:MM') {
        if (format === 'HH:MM') {
            return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } else if (format === 'MM-DD') {
            return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        } else {
            return date.toLocaleString();
        }
    }
    
    // 模拟设备告警数据
    window.getDeviceAlarms = function(count = 5) {
        const alarmTypes = [
            '功率因数过低', '温度过高', '电压偏差', '电流过载', '频率偏差',
            '调压异常', '变压器油温高', '铁芯温度高', '馈线过流', '馈线过压'
        ];
        
        const deviceNames = [
            '1号牵引变压器', '2号牵引变压器', '1号自耦变压器', '2号自耦变压器',
            '212馈线', '212B馈线', '214馈线', '215馈线'
        ];
        
        const statusTypes = ['待处理', '已确认', '已处理', '已忽略'];
        
        const alarms = [];
        const now = new Date();
        
        for (let i = 0; i < count; i++) {
            const alarmType = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];
            const deviceName = deviceNames[Math.floor(Math.random() * deviceNames.length)];
            
            let alarmValue, threshold;
            switch (alarmType) {
                case '功率因数过低':
                    alarmValue = generateRandomInRange(0.82, 0.88, 2);
                    threshold = 0.90;
                    break;
                case '温度过高':
                    alarmValue = generateRandomInRange(70, 85, 1);
                    threshold = 70;
                    break;
                case '电压偏差':
                    alarmValue = generateRandomInRange(122, 125, 1);
                    threshold = 121;
                    break;
                case '电流过载':
                    alarmValue = generateRandomInRange(160, 180, 0);
                    threshold = 160;
                    break;
                default:
                    alarmValue = generateRandomInRange(0, 100, 1);
                    threshold = 50;
            }
            
            // 生成一个随机的过去时间（48小时内）
            const alarmTime = new Date(now.getTime() - Math.random() * 48 * 3600 * 1000);
            
            const severity = i % 3 === 0 ? 'critical' : i % 2 === 0 ? 'warning' : 'normal';
            const status = statusTypes[Math.floor(Math.random() * statusTypes.length)];
            
            alarms.push({
                time: alarmTime.toLocaleString(),
                device: deviceName,
                type: alarmType,
                value: alarmValue,
                threshold: threshold,
                status: status,
                severity: severity
            });
        }
        
        // 按时间排序，最新的在前
        return alarms.sort((a, b) => new Date(b.time) - new Date(a.time));
    };
})();
