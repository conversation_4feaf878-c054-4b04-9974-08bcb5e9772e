/* 页面切换动画改进 */
.page {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: none;
}

.page.active {
    display: block;
    animation: pageEnter 0.5s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes pageEnter {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片载入动画 */
.card {
    opacity: 0;
    transform: translateY(20px);
    animation: cardEnter 0.5s forwards;
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes cardEnter {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 依次延迟加载卡片 */
.dashboard-grid .card:nth-child(1) {
    animation-delay: 0.1s;
}

.dashboard-grid .card:nth-child(2) {
    animation-delay: 0.2s;
}

.dashboard-grid .card:nth-child(3) {
    animation-delay: 0.3s;
}

.dashboard-grid .card:nth-child(4) {
    animation-delay: 0.4s;
}

/* 导航菜单高亮动画 */
nav ul li a {
    position: relative;
    overflow: hidden;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #5d90ff;
    transition: width 0.3s ease;
}

nav ul li a:hover::after {
    width: 100%;
}

nav ul li a.active::after {
    width: 100%;
    height: 3px;
    box-shadow: 0 0 8px rgba(93, 144, 255, 0.8);
}

/* 数据表格行动画 */
.data-table tr {
    transition: all 0.3s ease;
}

/* 告警表格更新闪烁效果 */
.data-table.flash-update {
    animation: tableFlash 1s ease;
}

@keyframes tableFlash {
    0%, 100% { 
        box-shadow: 0 0 0 rgba(93, 144, 255, 0); 
    }
    50% { 
        box-shadow: 0 0 15px rgba(93, 144, 255, 0.8); 
    }
}

/* 表格行更新效果 */
.data-table tr.new-entry {
    animation: newEntry 2s ease;
}

@keyframes newEntry {
    0% { 
        background-color: rgba(93, 144, 255, 0.3); 
    }
    100% { 
        background-color: transparent;
    }
}

/* 图表切换时的过渡动画 */
.chart {
    transition: height 0.3s ease, opacity 0.3s ease;
}

/* 加载中状态 */
.loading {
    position: relative;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #5d90ff, #36a3ff, #4cd2a0, #ffcc33);
    background-size: 300% 100%;
    animation: loading 2s infinite;
    z-index: 10;
}

@keyframes loading {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
