// 初始化所有图表
function initAllCharts() {
    // 能耗总览页面图表
    initPowerChart();
    initPowerFactorGauge();
    initEnergyDistribution();
    initEnergyConsumption();
    
    // 变压器监控页面图表
    initTransformerPower();
    initTransformerTemp();
    initAutotransformerTemp();
    initTransformerCurrent();
    
    // 馈线能耗页面图表
    initFeederCurrent();
    initFeederVoltage();
    initFeederComparison();
    initFeederImpedance();
    
    // 能耗分析页面图表
    initEnergyTrend();
    initLoadDistribution();
    initPowerFactorAnalysis();
    initEnergyAnomaly();
}

// ==================== 能耗总览页面图表 ====================

// 初始化实时功率监控图表
function initPowerChart() {
    const chartDom = document.getElementById('power-chart');
    if (!chartDom) return;
    
    window.powerChart = echarts.init(chartDom);
    
    const now = new Date();
    const data = [];
    const reactiveData = [];
    
    // 生成过去30分钟的模拟数据
    for (let i = 0; i < 30; i++) {
        const time = new Date(now - (30 - i) * 60 * 1000);
        const value = Math.random() * 200 + 300;
        const reactiveValue = value * 0.7;
        
        data.push({
            name: time.toString(),
            value: [time, value]
        });
        
        reactiveData.push({
            name: time.toString(),
            value: [time, reactiveValue]
        });
    }
    
    const option = {
        title: {
            text: '实时功率监控',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                const time = new Date(params[0].value[0]);
                let result = time.getHours() + ':' + time.getMinutes() + '<br>';
                
                params.forEach(param => {
                    result += param.marker + ' ' + param.seriesName + ': ' + param.value[1].toFixed(2) + (param.seriesIndex === 0 ? 'kW' : 'kVar') + '<br>';
                });
                
                return result;
            }
        },
        legend: {
            data: ['有功功率', '无功功率'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'time',
            splitLine: {
                show: false
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '有功功率(kW)',
                splitLine: {
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            },
            {
                type: 'value',
                name: '无功功率(kVar)',
                splitLine: {
                    show: false
                }
            }
        ],
        series: [
            {
                name: '有功功率',
                type: 'line',
                smooth: true,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: '#1e88e5'
                },
                data: data
            },
            {
                name: '无功功率',
                type: 'line',
                yAxisIndex: 1,
                smooth: true,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: '#fb8c00'
                },
                data: reactiveData
            }
        ]
    };
    
    window.powerChart.setOption(option);
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        window.powerChart.resize();
    });
}

// 初始化功率因数仪表盘
function initPowerFactorGauge() {
    const chartDom = document.getElementById('power-factor-gauge');
    if (!chartDom) return;
    
    window.powerFactorGauge = echarts.init(chartDom);
    
    const option = {
        series: [
            {
                type: 'gauge',
                min: 0.7,
                max: 1,
                radius: '90%',
                axisLine: {
                    lineStyle: {
                        width: 30,
                        color: [
                            [0.3, '#ff4500'],
                            [0.7, '#ffcc00'],
                            [1, '#5cb85c']
                        ]
                    }
                },
                pointer: {
                    itemStyle: {
                        color: 'auto'
                    }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: {
                        color: '#fff',
                        width: 2
                    }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: {
                        color: '#fff',
                        width: 4
                    }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 14
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}',
                    color: 'auto',
                    fontSize: 24
                },
                data: [
                    {
                        value: 0.92,
                        name: '功率因数'
                    }
                ]
            }
        ]
    };
    
    window.powerFactorGauge.setOption(option);
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        window.powerFactorGauge.resize();
    });
}

// 初始化能耗分布饼图
function initEnergyDistribution() {
    const chartDom = document.getElementById('energy-distribution');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 10,
            top: 'center',
            data: ['1号牵引变压器', '2号牵引变压器', '动力变压器', '1号自耦变压器', '2号自耦变压器', '3号自耦变压器', '4号自耦变压器']
        },
        series: [
            {
                name: '能耗分布',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['65%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 335, name: '1号牵引变压器' },
                    { value: 310, name: '2号牵引变压器' },
                    { value: 234, name: '动力变压器' },
                    { value: 135, name: '自耦变压器' },
                    { value: 148, name: '其他设备' }
                ]
            }
        ]
    };
    
    const energyDistributionChart = echarts.init(chartDom);
    energyDistributionChart.setOption(option);
    window.energyDistributionChart = energyDistributionChart;
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        if (window.energyDistributionChart) {
            window.energyDistributionChart.resize();
        }
    });
}

// 初始化累计电能消耗图表
function initEnergyConsumption() {
    const chartDom = document.getElementById('energy-consumption');
    if (!chartDom) return;
    
    window.energyConsumptionChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: '月度电能消耗',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['有功电能', '无功电能'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['1日', '2日', '3日', '4日', '5日', '6日', '7日', '8日', '9日', '10日', 
                   '11日', '12日', '13日', '14日', '15日', '16日', '17日', '18日', '19日', '20日',
                   '21日', '22日', '23日', '24日', '25日', '26日', '27日', '28日', '29日', '30日']
        },
        yAxis: {
            type: 'value',
            name: '电能(kWh)'
        },
        series: [
            {
                name: '有功电能',
                type: 'bar',
                stack: 'total',
                itemStyle: {
                    color: '#4caf50'
                },
                data: Array.from({length: 30}, () => Math.floor(Math.random() * 100) + 50)
            },
            {
                name: '无功电能',
                type: 'bar',
                stack: 'total',
                itemStyle: {
                    color: '#ff9800'
                },
                data: Array.from({length: 30}, () => Math.floor(Math.random() * 60) + 30)
            }
        ]
    };
    
    window.energyConsumptionChart.setOption(option);
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        window.energyConsumptionChart.resize();
    });
}

// ==================== 变压器监控页面图表 ====================

// 初始化变压器功率监控图表
function initTransformerPower() {
    const chartDom = document.getElementById('transformer-power');
    if (!chartDom) return;
    
    window.transformerPowerChart = echarts.init(chartDom);
    
    // 获取变压器选择器元素
    const transformerSelector = document.getElementById('transformer-selector');
    if (transformerSelector) {
        transformerSelector.addEventListener('change', function() {
            updateTransformerPowerChart(this.value);
        });
    }
    
    function updateTransformerPowerChart(transformerId) {
        // 这里应该是从后端API获取数据，现在使用模拟数据
        const activeData = [];
        const reactiveData = [];
        
        // 基于选择的变压器ID生成不同的模拟数据
        let baseActivePower, baseReactivePower;
        
        if (transformerId === '1B') {
            baseActivePower = 320;
            baseReactivePower = 180;
        } else if (transformerId === '2B') {
            baseActivePower = 280;
            baseReactivePower = 140;
        } else if (transformerId === 'auxiliary') {
            // 动力变压器功率通常较小
            baseActivePower = 120;
            baseReactivePower = 60;
        }
        
        for (let i = 0; i < 24; i++) {
            const hourFactor = (i >= 9 && i <= 18) ? 1.3 : 0.8; // 工作时间功率更高
            
            activeData.push({
                name: i + ':00',
                value: Math.round((baseActivePower + Math.random() * 50) * hourFactor)
            });
            
            reactiveData.push({
                name: i + ':00',
                value: Math.round((baseReactivePower + Math.random() * 30) * hourFactor)
            });
        }
        
        const option = {
            title: {
                text: transformerId === '1B' ? '1B_牵引变压器功率监控' : '2B_牵引变压器功率监控',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#a0b4e6'
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += param.marker + ' ' + param.seriesName + ': ' + 
                                 param.value + (param.seriesIndex === 0 ? ' kW' : ' kVar') + '<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['有功功率', '无功功率'],
                bottom: 0,
                textStyle: {
                    color: '#a0b4e6'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: activeData.map(item => item.name),
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            yAxis: {
                type: 'value',
                name: '功率',
                splitLine: {
                    lineStyle: {
                        color: '#26355c'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            series: [{
                name: '有功功率',
                data: activeData.map(item => item.value),
                type: 'line',
                smooth: true,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(55, 162, 255, 0.6)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(55, 162, 255, 0)'
                        }
                    ])
                },
                itemStyle: {
                    color: '#37a2ff'
                },
                lineStyle: {
                    width: 2,
                    color: '#37a2ff'
                }
            },
            {
                name: '无功功率',
                data: reactiveData.map(item => item.value),
                type: 'line',
                smooth: true,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(255, 112, 55, 0.6)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(255, 112, 55, 0)'
                        }
                    ])
                },
                itemStyle: {
                    color: '#ff7037'
                },
                lineStyle: {
                    width: 2,
                    color: '#ff7037'
                }
            }]
        };
        
        transformerPowerChart.setOption(option);
        
        // 更新指标数据显示
        updateTransformerMetrics(transformerId);
    }
    
    function updateTransformerMetrics(transformerId) {
        const activePowerElement = document.getElementById('transformer-active-power');
        const reactivePowerElement = document.getElementById('transformer-reactive-power');
        const powerFactorElement = document.getElementById('transformer-power-factor');
        
        if (activePowerElement && reactivePowerElement && powerFactorElement) {
            // 根据变压器ID设置不同的值
            let activePower, reactivePower, powerFactor;
            
            if (transformerId === '1B') {
                activePower = '345 kW';
                reactivePower = '185 kVar';
                powerFactor = '0.91';
            } else if (transformerId === '2B') {
                activePower = '295 kW';
                reactivePower = '162 kVar';
                powerFactor = '0.88';
            } else if (transformerId === 'auxiliary') {
                activePower = '118 kW';
                reactivePower = '64 kVar';
                powerFactor = '0.92';
            }
            
            activePowerElement.textContent = activePower;
            reactivePowerElement.textContent = reactivePower;
            powerFactorElement.textContent = powerFactor;
        }
    }
    
    // 初始加载第一个变压器数据
    updateTransformerPowerChart('1B');
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        transformerPowerChart.resize();
    });
}

// 初始化变压器负载率图表
function initTransformerLoad() {
    // 其他图表的初始化代码类似，这里省略
    console.log('初始化变压器负载率图表');
}

// 初始化变压器温度监测图表
function initTransformerTemp() {
    const chartDom = document.getElementById('transformer-temp');
    if (!chartDom) return;
    
    window.transformerTempChart = echarts.init(chartDom);
    
    // 获取变压器温度选择器
    const transformerTempSelector = document.getElementById('transformer-temp-selector');
    if (transformerTempSelector) {
        transformerTempSelector.addEventListener('change', function() {
            updateTransformerTempChart(this.value);
        });
    }
    
    function updateTransformerTempChart(transformerNum) {
        // 模拟数据 - 根据主变编号选择不同的温度数据
        const oilTempBase = transformerNum === '1' ? 65 : 68;
        const copperTempBase = transformerNum === '1' ? 75 : 78;
        
        const times = [];
        const oilTempData = [];
        const copperTempData = [];
        
        // 生成24小时的温度数据
        for (let i = 0; i < 24; i++) {
            times.push(i + ':00');
            
            // 模拟温度波动 - 白天温度较高
            const timeEffect = (i >= 10 && i <= 18) ? 1.1 : 0.9;
            
            // 添加随机波动
            oilTempData.push((oilTempBase + Math.random() * 5) * timeEffect);
            copperTempData.push((copperTempBase + Math.random() * 6) * timeEffect);
        }
        
        const option = {
            title: {
                text: transformerNum + '号主变温度监测',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#a0b4e6'
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += param.marker + ' ' + param.seriesName + ': ' + 
                                 param.value.toFixed(1) + ' ℃<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['油温', '铜温'],
                bottom: 0,
                textStyle: {
                    color: '#a0b4e6'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: times,
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            yAxis: {
                type: 'value',
                name: '温度(℃)',
                min: 'dataMin',
                splitLine: {
                    lineStyle: {
                        color: '#26355c'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            series: [
                {
                    name: '油温',
                    type: 'line',
                    data: oilTempData,
                    smooth: true,
                    lineStyle: {
                        width: 3,
                        color: '#ff7037'
                    },
                    itemStyle: {
                        color: '#ff7037'
                    },
                    markLine: {
                        data: [
                            { 
                                name: '油温告警线',
                                yAxis: 80,
                                lineStyle: { 
                                    color: '#ff4d4f', 
                                    type: 'dashed'
                                },
                                label: {
                                    formatter: '油温告警线: 80℃',
                                    position: 'insideEndTop'
                                }
                            }
                        ]
                    }
                },
                {
                    name: '铜温',
                    type: 'line',
                    data: copperTempData,
                    smooth: true,
                    lineStyle: {
                        width: 3,
                        color: '#ffc53d'
                    },
                    itemStyle: {
                        color: '#ffc53d'
                    },
                    markLine: {
                        data: [
                            { 
                                name: '铜温告警线',
                                yAxis: 90,
                                lineStyle: { 
                                    color: '#ff4d4f', 
                                    type: 'dashed' 
                                },
                                label: {
                                    formatter: '铜温告警线: 90℃',
                                    position: 'insideEndTop'
                                }
                            }
                        ]
                    }
                }
            ]
        };
        
        transformerTempChart.setOption(option);
        
        // 更新指标数据
        updateTransformerTempMetrics(transformerNum);
    }
    
    function updateTransformerTempMetrics(transformerNum) {
        const oilTempElement = document.getElementById('transformer-oil-temp');
        const copperTempElement = document.getElementById('transformer-copper-temp');
        
        if (oilTempElement && copperTempElement) {
            // 根据主变编号设置不同的温度值
            const oilTemp = transformerNum === '1' ? '66.5 ℃' : '69.2 ℃';
            const copperTemp = transformerNum === '1' ? '77.3 ℃' : '80.1 ℃';
            
            oilTempElement.textContent = oilTemp;
            copperTempElement.textContent = copperTemp;
        }
    }
    
    // 初始加载第一个主变的温度数据
    updateTransformerTempChart('1');
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        transformerTempChart.resize();
    });
}

// 初始化变压器效率分析图表
function initTransformerEfficiency() {
    console.log('初始化变压器效率分析图表');
}

// 初始化自耦变压器温度监测图表
function initAutotransformerTemp() {
    const chartDom = document.getElementById('autotransformer-temp');
    if (!chartDom) return;
    
    window.autotransformerTempChart = echarts.init(chartDom);
    
    // 生成24小时的温度数据
    const times = [];
    const at1TempData = [];
    const at2TempData = [];
    const at3TempData = [];
    const at4TempData = [];
    
    for (let i = 0; i < 24; i++) {
        times.push(i + ':00');
        
        // 模拟温度波动 - 白天温度较高
        const timeEffect = (i >= 10 && i <= 18) ? 1.1 : 0.9;
        
        // 基础温度值 + 随机波动
        at1TempData.push((55 + Math.random() * 4) * timeEffect);
        at2TempData.push((57 + Math.random() * 4) * timeEffect);
        at3TempData.push((54 + Math.random() * 4) * timeEffect);
        at4TempData.push((56 + Math.random() * 4) * timeEffect);
    }
    
    const option = {
        title: {
            text: '自耦变压器温度监测',
            left: 'center',
            textStyle: {
                fontSize: 14,
                color: '#a0b4e6'
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    result += param.marker + ' ' + param.seriesName + ': ' + 
                             param.value.toFixed(1) + ' ℃<br/>';
                });
                return result;
            }
        },
        legend: {
            data: ['1AT温度', '2AT温度', '3AT温度', '4AT温度'],
            bottom: 0,
            textStyle: {
                color: '#a0b4e6'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: times,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#a0b4e6'
            }
        },
        yAxis: {
            type: 'value',
            name: '温度(℃)',
            min: 'dataMin',
            max: 'dataMax',
            splitLine: {
                lineStyle: {
                    color: '#26355c'
                }
            },
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#a0b4e6'
            }
        },
        series: [
            {
                name: '1AT温度',
                type: 'line',
                data: at1TempData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#36cfc9'
                }
            },
            {
                name: '2AT温度',
                type: 'line',
                data: at2TempData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#40a9ff'
                }
            },
            {
                name: '3AT温度',
                type: 'line',
                data: at3TempData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#9254de'
                }
            },
            {
                name: '4AT温度',
                type: 'line',
                data: at4TempData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#f759ab'
                }
            }
        ]
    };
    
    autotransformerTempChart.setOption(option);
    
    // 更新指标数据
    updateAutotransformerTempMetrics();
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        autotransformerTempChart.resize();
    });
}

function updateAutotransformerTempMetrics() {
    const at1TempElement = document.getElementById('autotransformer-temp-1');
    const at2TempElement = document.getElementById('autotransformer-temp-2');
    const at3TempElement = document.getElementById('autotransformer-temp-3');
    const at4TempElement = document.getElementById('autotransformer-temp-4');
    
    if (at1TempElement && at2TempElement && at3TempElement && at4TempElement) {
        at1TempElement.textContent = '56.2 ℃';
        at2TempElement.textContent = '58.5 ℃';
        at3TempElement.textContent = '55.3 ℃';
        at4TempElement.textContent = '57.1 ℃';
    }
}

// 初始化变压器电流监测图表
function initTransformerCurrent() {
    const chartDom = document.getElementById('transformer-current');
    if (!chartDom) return;
    
    window.transformerCurrentChart = echarts.init(chartDom);
    
    // 生成模拟的电流数据
    const times = [];
    const phaseAData = [];
    const phaseBData = [];
    const phaseCData = [];
    
    for (let i = 0; i < 24; i++) {
        times.push(i + ':00');
        
        // 模拟电流波动 - 工作时间电流更大
        const loadFactor = (i >= 8 && i <= 20) ? 1.3 : 0.8;
        
        // 基础电流值 + 随机波动
        const baseValue = 500 * loadFactor;
        phaseAData.push(Math.round(baseValue + Math.random() * 50));
        phaseBData.push(Math.round(baseValue - 20 + Math.random() * 50));
        phaseCData.push(Math.round(baseValue + 20 + Math.random() * 50));
    }
    
    const option = {
        title: {
            text: '变压器高压侧电流监测',
            left: 'center',
            textStyle: {
                fontSize: 14,
                color: '#a0b4e6'
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    result += param.marker + ' ' + param.seriesName + ': ' + 
                             param.value + ' A<br/>';
                });
                return result;
            }
        },
        legend: {
            data: ['A相电流', 'B相电流', 'C相电流'],
            bottom: 0,
            textStyle: {
                color: '#a0b4e6'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: times,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#a0b4e6'
            }
        },
        yAxis: {
            type: 'value',
            name: '电流(A)',
            splitLine: {
                lineStyle: {
                    color: '#26355c'
                }
            },
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#a0b4e6'
            }
        },
        series: [
            {
                name: 'A相电流',
                type: 'line',
                data: phaseAData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#ff7037'
                }
            },
            {
                name: 'B相电流',
                type: 'line',
                data: phaseBData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#ffaa00'
                }
            },
            {
                name: 'C相电流',
                type: 'line',
                data: phaseCData,
                smooth: true,
                lineStyle: {
                    width: 2,
                    color: '#52c41a'
                }
            }
        ]
    };
    
    transformerCurrentChart.setOption(option);
    
    // 更新指标数据
    updateTransformerCurrentMetrics();
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        transformerCurrentChart.resize();
    });
}

function updateTransformerCurrentMetrics() {
    const phaseAElement = document.getElementById('transformer-current-ha');
    const phaseBElement = document.getElementById('transformer-current-hb');
    const phaseCElement = document.getElementById('transformer-current-hc');
    
    if (phaseAElement && phaseBElement && phaseCElement) {
        phaseAElement.textContent = '523 A';
        phaseBElement.textContent = '518 A';
        phaseCElement.textContent = '535 A';
    }
}

// ==================== 馈线能耗页面图表 ====================

// 初始化馈线电流监测图表
function initFeederCurrent() {
    const chartDom = document.getElementById('feeder-current');
    if (!chartDom) return;
    
    window.feederCurrentChart = echarts.init(chartDom);
    
    // 获取馈线选择器元素
    const feederSelector = document.getElementById('feeder-selector');
    if (feederSelector) {
        feederSelector.addEventListener('change', function() {
            updateFeederCurrentChart(this.value);
        });
    }
    
    function updateFeederCurrentChart(feederId) {
        // 基于选择的馈线ID生成不同的模拟电流数据
        const times = [];
        const tLineCurrentData = [];
        const fLineCurrentData = [];
        
        // 不同馈线的基础电流值
        let tLineBase, fLineBase;
        
        switch(feederId) {
            case '212':
                tLineBase = 380;
                fLineBase = 360;
                break;
            case '212B':
                tLineBase = 360;
                fLineBase = 350;
                break;
            case '214':
                tLineBase = 420;
                fLineBase = 410;
                break;
            case '215':
                tLineBase = 400;
                fLineBase = 390;
                break;
            case '216':
                tLineBase = 370;
                fLineBase = 365;
                break;
            case '219':
                tLineBase = 450;
                fLineBase = 435;
                break;
            case '220':
                tLineBase = 430;
                fLineBase = 420;
                break;
            case '221':
                tLineBase = 440;
                fLineBase = 425;
                break;
            case '222':
                tLineBase = 410;
                fLineBase = 400;
                break;
            case '223':
                tLineBase = 390;
                fLineBase = 380;
                break;
            default: // 全部馈线
                tLineBase = 400;
                fLineBase = 385;
        }
        
        for (let i = 0; i < 24; i++) {
            times.push(i + ':00');
            
            // 模拟电流波动 - 工作时间电流更大
            const loadFactor = (i >= 8 && i <= 20) ? 1.3 : 0.8;
            
            // 应用负载因子和随机波动
            tLineCurrentData.push(Math.round((tLineBase + Math.random() * 30) * loadFactor));
            fLineCurrentData.push(Math.round((fLineBase + Math.random() * 30) * loadFactor));
        }
        
        const option = {
            title: {
                text: feederId === 'all' ? '所有馈线电流监测' : feederId + '馈线电流监测',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#a0b4e6'
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += param.marker + ' ' + param.seriesName + ': ' + 
                                 param.value + ' A<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['T线电流', 'F线电流'],
                bottom: 0,
                textStyle: {
                    color: '#a0b4e6'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: times,
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            yAxis: {
                type: 'value',
                name: '电流(A)',
                splitLine: {
                    lineStyle: {
                        color: '#26355c'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            series: [
                {
                    name: 'T线电流',
                    type: 'line',
                    data: tLineCurrentData,
                    smooth: true,
                    lineStyle: {
                        width: 2,
                        color: '#1890ff'
                    }
                },
                {
                    name: 'F线电流',
                    type: 'line',
                    data: fLineCurrentData,
                    smooth: true,
                    lineStyle: {
                        width: 2,
                        color: '#52c41a'
                    }
                }
            ]
        };
        
        feederCurrentChart.setOption(option);
        
        // 如果是选择了单个馈线，则需要更新表格数据
        if (feederId !== 'all') {
            updateFeederTableData(feederId);
        }
    }
    
    // 初始加载
    updateFeederCurrentChart('all');
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        feederCurrentChart.resize();
    });
}

// 初始化馈线电压监测图表
function initFeederVoltage() {
    const chartDom = document.getElementById('feeder-voltage');
    if (!chartDom) return;
    
    window.feederVoltageChart = echarts.init(chartDom);
    
    // 获取馈线选择器元素以响应变化
    const feederSelector = document.getElementById('feeder-selector');
    if (feederSelector) {
        feederSelector.addEventListener('change', function() {
            updateFeederVoltageChart(this.value);
        });
    }
    
    function updateFeederVoltageChart(feederId) {
        // 基于选择的馈线ID生成不同的模拟电压数据
        const times = [];
        const tLineVoltageData = [];
        const fLineVoltageData = [];
        
        // 不同馈线的基础电压值
        let tLineBase = 27500; // 基础值27.5kV
        let fLineBase = 27300;
        
        if (feederId !== 'all') {
            // 为不同馈线设置略微不同的电压值
            const offset = parseInt(feederId.replace(/\D/g, '')) * 10;
            tLineBase += offset;
            fLineBase += offset;
        }
        
        for (let i = 0; i < 24; i++) {
            times.push(i + ':00');
            
            // 电压波动较小
            tLineVoltageData.push(Math.round(tLineBase + (Math.random() * 200 - 100)));
            fLineVoltageData.push(Math.round(fLineBase + (Math.random() * 200 - 100)));
        }
        
        const option = {
            title: {
                text: feederId === 'all' ? '所有馈线电压监测' : feederId + '馈线电压监测',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#a0b4e6'
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += param.marker + ' ' + param.seriesName + ': ' + 
                                 param.value + ' V<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['T线电压', 'F线电压'],
                bottom: 0,
                textStyle: {
                    color: '#a0b4e6'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: times,
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            yAxis: {
                type: 'value',
                name: '电压(V)',
                min: 26800,
                max: 28200,
                splitLine: {
                    lineStyle: {
                        color: '#26355c'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6',
                    formatter: function(value) {
                        return value / 1000 + 'kV';
                    }
                }
            },
            series: [
                {
                    name: 'T线电压',
                    type: 'line',
                    data: tLineVoltageData,
                    smooth: true,
                    lineStyle: {
                        width: 2,
                        color: '#722ed1'
                    }
                },
                {
                    name: 'F线电压',
                    type: 'line',
                    data: fLineVoltageData,
                    smooth: true,
                    lineStyle: {
                        width: 2,
                        color: '#eb2f96'
                    }
                }
            ]
        };
        
        feederVoltageChart.setOption(option);
    }
    
    // 初始加载
    updateFeederVoltageChart('all');
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        feederVoltageChart.resize();
    });
}

// 初始化馈线参数对比图表
function initFeederComparison() {
    const chartDom = document.getElementById('feeder-comparison');
    if (!chartDom) return;
    
    window.feederComparisonChart = echarts.init(chartDom);
    
    const feederIds = ['212', '212B', '214', '215', '216', '219', '220', '221', '222', '223'];
    const feederNames = feederIds.map(id => id + '馈线');
    
    // 生成模拟的电流和阻抗数据
    const currents = feederIds.map(() => Math.round(300 + Math.random() * 200));
    const impedances = feederIds.map(() => (Math.random() * 0.5 + 1.2).toFixed(2));
    
    const option = {
        title: {
            text: '馈线参数对比',
            left: 'center',
            textStyle: {
                fontSize: 14,
                color: '#a0b4e6'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['T线电流(A)', '阻抗(Ω)'],
            bottom: 0,
            textStyle: {
                color: '#a0b4e6'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: feederNames,
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6',
                    rotate: 30
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '电流(A)',
                position: 'left',
                splitLine: {
                    lineStyle: {
                        color: '#26355c'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            {
                type: 'value',
                name: '阻抗(Ω)',
                position: 'right',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            }
        ],
        series: [
            {
                name: 'T线电流(A)',
                type: 'bar',
                yAxisIndex: 0,
                data: currents,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#1890ff' },
                        { offset: 1, color: '#096dd9' }
                    ])
                }
            },
            {
                name: '阻抗(Ω)',
                type: 'line',
                yAxisIndex: 1,
                data: impedances,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                    width: 2,
                    color: '#fa541c'
                },
                itemStyle: {
                    color: '#fa541c'
                }
            }
        ]
    };
    
    feederComparisonChart.setOption(option);
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        feederComparisonChart.resize();
    });
}

// 初始化馈线阻抗分析图表
function initFeederImpedance() {
    const chartDom = document.getElementById('feeder-impedance');
    if (!chartDom) return;
    
    window.feederImpedanceChart = echarts.init(chartDom);
    
    // 获取馈线选择器元素以响应变化
    const feederSelector = document.getElementById('feeder-selector');
    if (feederSelector) {
        feederSelector.addEventListener('change', function() {
            updateFeederImpedanceChart(this.value);
        });
    }
    
    function updateFeederImpedanceChart(feederId) {
        // 为极坐标图准备数据
        let data;
        
        if (feederId === 'all') {
            // 所有馈线的阻抗和角度分布
            data = [
                { value: [1.35, 75], name: '212馈线' },
                { value: [1.42, 78], name: '212B馈线' },
                { value: [1.28, 72], name: '214馈线' },
                { value: [1.33, 76], name: '215馈线' },
                { value: [1.45, 77], name: '216馈线' },
                { value: [1.51, 79], name: '219馈线' },
                { value: [1.38, 75], name: '220馈线' },
                { value: [1.43, 80], name: '221馈线' },
                { value: [1.47, 76], name: '222馈线' },
                { value: [1.32, 74], name: '223馈线' }
            ];
        } else {
            // 单个馈线的24小时阻抗和角度变化
            data = [];
            // 根据馈线ID设定基础值
            let impedanceBase = 1.3 + (parseInt(feederId.replace(/\D/g, '')) % 10) * 0.02;
            let angleBase = 75 + (parseInt(feederId.replace(/\D/g, '')) % 10);
            
            for (let i = 0; i < 24; i++) {
                const time = i + ':00';
                // 阻抗和角度的随机变化
                const impedance = impedanceBase + (Math.random() * 0.2 - 0.1);
                const angle = angleBase + (Math.random() * 6 - 3);
                
                data.push({
                    value: [impedance, angle],
                    name: time
                });
            }
        }
        
        const option = {
            title: {
                text: feederId === 'all' ? '馈线阻抗分布' : feederId + '馈线阻抗变化',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#a0b4e6'
                }
            },
            legend: {
                data: data.map(item => item.name),
                bottom: 0,
                textStyle: {
                    color: '#a0b4e6'
                },
                type: 'scroll',
                pageButtonItemGap: 5,
                pageButtonPosition: 'end'
            },
            polar: {
                center: ['50%', '50%']
            },
            tooltip: {
                formatter: function(params) {
                    return params.seriesName + '<br/>' + 
                           params.marker + params.data.name + '<br/>' +
                           '阻抗: ' + params.data.value[0].toFixed(2) + 'Ω<br/>' +
                           '角度: ' + params.data.value[1].toFixed(1) + '°';
                }
            },
            angleAxis: {
                type: 'value',
                min: 0,
                max: 90,
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            radiusAxis: {
                axisLine: {
                    lineStyle: {
                        color: '#4c5c84'
                    }
                },
                axisLabel: {
                    color: '#a0b4e6'
                }
            },
            series: [
                {
                    name: '阻抗与角度',
                    type: 'scatter',
                    coordinateSystem: 'polar',
                    symbolSize: function(val) {
                        return feederId === 'all' ? 12 : 8;
                    },
                    data: data,
                    itemStyle: {
                        color: function(params) {
                            // 使用不同颜色区分不同馈线或时段
                            const colors = ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#fa8c16', 
                                           '#fa541c', '#722ed1', '#eb2f96', '#2f54eb', '#fadb14'];
                            if (feederId === 'all') {
                                return colors[params.dataIndex % colors.length];
                            } else {
                                // 根据角度值使用渐变色
                                const angle = params.data.value[1];
                                const index = Math.floor((angle - 70) / 2);
                                return colors[(index + 5) % colors.length];
                            }
                        }
                    }
                }
            ]
        };
        
        feederImpedanceChart.setOption(option);
    }
    
    // 初始加载
    updateFeederImpedanceChart('all');
    
    // 添加响应式调整
    window.addEventListener('resize', function() {
        feederImpedanceChart.resize();
    });
}

// 更新馈线详细参数表格数据
function updateFeederTableData(feederId) {
    const tbody = document.getElementById('feeder-data-tbody');
    if (!tbody) return;
    
    // 清空表格
    tbody.innerHTML = '';
    
    // 如果选择全部馈线，显示所有馈线数据
    const feederIds = feederId === 'all' ? 
        ['212', '212B', '214', '215', '216', '219', '220', '221', '222', '223'] : 
        [feederId];
    
    feederIds.forEach(id => {
        // 为每个馈线生成模拟数据
        const idNum = parseInt(id.replace(/\D/g, ''));
        const tVoltage = Math.round(27500 + idNum * 10 + (Math.random() * 100 - 50));
        const tCurrent = Math.round(400 + idNum * 5 + (Math.random() * 50 - 25));
        const fVoltage = Math.round(27300 + idNum * 10 + (Math.random() * 100 - 50));
        const fCurrent = Math.round(380 + idNum * 5 + (Math.random() * 50 - 25));
        const impedance = (1.3 + idNum * 0.02 + Math.random() * 0.1).toFixed(2);
        const angle = Math.round(75 + idNum % 5 + Math.random() * 2);
        
        // 创建表格行
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${id}馈线</td>
            <td>${tVoltage}</td>
            <td>${tCurrent}</td>
            <td>${fVoltage}</td>
            <td>${fCurrent}</td>
            <td>${impedance}</td>
            <td>${angle}</td>
        `;
        
        tbody.appendChild(tr);
    });
}

// ==================== 能耗分析页面图表 ====================

// 初始化能耗趋势分析图表
function initEnergyTrend() {
    const chartDom = document.getElementById('energy-trend');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    
    // 生成日期数据
    const dates = [];
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
        const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = month.toLocaleString('zh-CN', { month: 'short' });
        dates.push(monthName);
    }
    
    // 模拟年度能耗数据
    const data = [
        2100, 2350, 2600, 2800, 2950, 3200, 3450, 3600, 3300, 2950, 2700, 2400
    ];
    
    // 生成同比数据 (去年同期数据)
    const lastYearData = data.map(value => value * 0.9 + Math.random() * 200 - 100);
    
    const option = {
        title: {
            text: '年度能耗趋势分析',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                    result += param.marker + ' ' + param.seriesName + ': ' + param.value + ' kWh<br/>';
                });
                return result;
            }
        },
        legend: {
            data: ['今年', '去年同期'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: dates,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            }
        },
        yAxis: {
            type: 'value',
            name: '能耗 (kWh)',
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#ddd'
                }
            }
        },
        series: [
            {
                name: '今年',
                type: 'line',
                data: data,
                lineStyle: {
                    color: '#1a7ced',
                    width: 3
                },
                itemStyle: {
                    color: '#1a7ced'
                },
                symbol: 'circle',
                symbolSize: 8,
                markPoint: {
                    data: [
                        { type: 'max', name: '最大值' },
                        { type: 'min', name: '最小值' }
                    ]
                }
            },
            {
                name: '去年同期',
                type: 'line',
                data: lastYearData,
                lineStyle: {
                    color: '#ff7037',
                    width: 2,
                    type: 'dashed'
                },
                itemStyle: {
                    color: '#ff7037'
                },
                symbol: 'circle',
                symbolSize: 6
            }
        ]
    };
    
    const energyTrendChart = echarts.init(chartDom);
    energyTrendChart.setOption(option);
    window.energyTrendChart = energyTrendChart;
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        if (window.energyTrendChart) {
            window.energyTrendChart.resize();
        }
    });
}

// 初始化峰谷平负载分析图表
function initLoadDistribution() {
    const chartDom = document.getElementById('load-distribution');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    
    // 模拟一天24小时的数据
    const hours = [];
    for (let i = 0; i < 24; i++) {
        hours.push(i + ':00');
    }
    
    // 模拟峰谷平时段的负载数据
    const peakHours = [7, 8, 9, 10, 11, 17, 18, 19, 20];
    const data = hours.map((hour, index) => {
        const hourNum = parseInt(hour);
        // 峰时段 (7-11点, 17-20点) 负载较高
        if (peakHours.includes(hourNum)) {
            return Math.round(Math.random() * 40 + 450);
        } 
        // 谷时段 (23-6点) 负载较低
        else if (hourNum >= 23 || hourNum <= 6) {
            return Math.round(Math.random() * 30 + 200);
        } 
        // 平时段 其他时间
        else {
            return Math.round(Math.random() * 30 + 350);
        }
    });
    
    const option = {
        title: {
            text: '24小时峰谷平负载分布',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].marker + ' 负载功率: ' + params[0].value + ' kW<br/>';
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: hours,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                interval: 2,
                color: '#666'
            }
        },
        yAxis: {
            type: 'value',
            name: '功率 (kW)',
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#ddd'
                }
            }
        },
        visualMap: {
            show: false,
            pieces: [
                {
                    gt: 400,
                    lte: 500,
                    color: '#ff7037'
                },
                {
                    gt: 300,
                    lte: 400,
                    color: '#ffaa00'
                },
                {
                    gt: 0,
                    lte: 300,
                    color: '#52c41a'
                }
            ]
        },
        series: [
            {
                name: '负载功率',
                type: 'bar',
                data: data,
                barWidth: '60%',
                itemStyle: {
                    borderRadius: [4, 4, 0, 0]
                },
                markLine: {
                    data: [
                        {
                            name: '平均值',
                            type: 'average',
                            lineStyle: {
                                color: '#1a7ced',
                                width: 2,
                                type: 'dashed'
                            },
                            label: {
                                position: 'end',
                                formatter: '平均: {c} kW'
                            }
                        }
                    ]
                }
            }
        ]
    };
    
    const loadDistributionChart = echarts.init(chartDom);
    loadDistributionChart.setOption(option);
    window.loadDistributionChart = loadDistributionChart;
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        if (window.loadDistributionChart) {
            window.loadDistributionChart.resize();
        }
    });
}

// 初始化功率因数优化分析图表
function initPowerFactorAnalysis() {
    const chartDom = document.getElementById('power-factor-analysis');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    
    // 模拟12个月的数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 模拟优化前后功率因数数据
    const beforeOptimization = months.map(() => (Math.random() * 0.1 + 0.8).toFixed(2));
    const afterOptimization = months.map((_, index) => {
        const before = parseFloat(beforeOptimization[index]);
        return Math.min((before + Math.random() * 0.12).toFixed(2), 0.98);
    });
    
    const option = {
        title: {
            text: '功率因数优化分析',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].axisValue + '<br/>' +
                       params[0].marker + ' ' + params[0].seriesName + ': ' + params[0].value + '<br/>' +
                       params[1].marker + ' ' + params[1].seriesName + ': ' + params[1].value;
            }
        },
        legend: {
            data: ['优化前', '优化后'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: months,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            }
        },
        yAxis: {
            type: 'value',
            name: '功率因数',
            min: 0.7,
            max: 1.0,
            interval: 0.05,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#ddd'
                }
            }
        },
        series: [
            {
                name: '优化前',
                type: 'line',
                data: beforeOptimization,
                lineStyle: {
                    color: '#ff7037'
                },
                itemStyle: {
                    color: '#ff7037'
                },
                symbol: 'circle',
                symbolSize: 8
            },
            {
                name: '优化后',
                type: 'line',
                data: afterOptimization,
                lineStyle: {
                    color: '#52c41a'
                },
                itemStyle: {
                    color: '#52c41a'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    
    const powerFactorAnalysisChart = echarts.init(chartDom);
    powerFactorAnalysisChart.setOption(option);
    window.powerFactorAnalysisChart = powerFactorAnalysisChart;
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        if (window.powerFactorAnalysisChart) {
            window.powerFactorAnalysisChart.resize();
        }
    });
}

// 初始化能耗异常分析图表
function initEnergyAnomaly() {
    const chartDom = document.getElementById('energy-anomaly');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    
    // 模拟最近30天的数据
    const days = [];
    const now = new Date();
    for (let i = 29; i >= 0; i--) {
        const day = new Date(now);
        day.setDate(now.getDate() - i);
        days.push(day.getDate() + '日');
    }
    
    // 模拟正常能耗数据
    const normalData = days.map(() => Math.round(Math.random() * 30 + 320));
    
    // 模拟实际能耗数据 (包括一些异常)
    const actualData = normalData.map((value, index) => {
        // 添加一些异常点
        if (index === 8 || index === 22) {
            return value * 1.3; // 明显高于预期
        } else if (index === 15) {
            return value * 0.7; // 明显低于预期
        } else {
            return value + Math.random() * 30 - 15; // 正常波动
        }
    });
    
    const option = {
        title: {
            text: '能耗异常分析',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                const date = params[0].axisValue;
                let result = date + '<br/>';
                
                params.forEach(param => {
                    result += param.marker + ' ' + param.seriesName + ': ' + param.value + ' kWh<br/>';
                });
                
                // 计算偏差
                const expected = params[0].value;
                const actual = params[1].value;
                const deviation = ((actual - expected) / expected * 100).toFixed(1);
                
                result += '<br/>偏差: ' + deviation + '%';
                
                // 添加异常提示
                if (Math.abs(deviation) > 15) {
                    result += '<br/><span style="color: #f44336; font-weight: bold">⚠️ 异常数据</span>';
                }
                
                return result;
            }
        },
        legend: {
            data: ['预期能耗', '实际能耗'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: days,
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                interval: 2,
                color: '#666'
            }
        },
        yAxis: {
            type: 'value',
            name: '能耗 (kWh)',
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            axisLabel: {
                color: '#666'
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#ddd'
                }
            }
        },
        series: [
            {
                name: '预期能耗',
                type: 'line',
                data: normalData,
                smooth: true,
                lineStyle: {
                    color: '#1a7ced',
                    width: 2
                },
                itemStyle: {
                    color: '#1a7ced'
                },
                symbol: 'circle',
                symbolSize: 6
            },
            {
                name: '实际能耗',
                type: 'line',
                data: actualData,
                smooth: true,
                lineStyle: {
                    color: '#ff7037',
                    width: 3
                },
                itemStyle: {
                    color: '#ff7037'
                },
                symbol: 'circle',
                symbolSize: 8,
                markPoint: {
                    data: [
                        // 标记异常点
                        { coord: [8, actualData[8]], value: '异常', symbolSize: 60 },
                        { coord: [15, actualData[15]], value: '异常', symbolSize: 60 },
                        { coord: [22, actualData[22]], value: '异常', symbolSize: 60 }
                    ],
                    symbol: 'pin',
                    itemStyle: {
                        color: '#f44336'
                    },
                    label: {
                        color: '#fff'
                    }
                }
            }
        ]
    };
    
    const energyAnomalyChart = echarts.init(chartDom);
    energyAnomalyChart.setOption(option);
    window.energyAnomalyChart = energyAnomalyChart;
    
    // 自适应窗口大小
    window.addEventListener('resize', function() {
        if (window.energyAnomalyChart) {
            window.energyAnomalyChart.resize();
        }
    });
}

// ==================== 设备状态监控页面图表 ====================

// 设备状态页面已移除，相关图表初始化函数已移除
