// 页面导航功能
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有导航链接
    const navLinks = document.querySelectorAll('nav a');
    
    // 为每个导航链接添加点击事件
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有导航链接的active类
            navLinks.forEach(l => l.classList.remove('active'));
            
            // 为当前点击的链接添加active类
            this.classList.add('active');
            
            // 获取要显示的页面ID
            const pageId = this.getAttribute('data-page');
            
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示选中的页面
            document.getElementById(pageId).classList.add('active');
            
            // 修复该页面的图表显示问题
            fixChartDisplay(pageId);
            
            // 触发窗口调整事件，确保图表正确渲染
            window.dispatchEvent(new Event('resize'));
        });
    });
    
    // 时间范围选择器事件
    const timeRangeSelector = document.getElementById('time-range');
    if (timeRangeSelector) {
        timeRangeSelector.addEventListener('change', function() {
            // 根据选择的时间范围更新图表数据
            updateChartsData(this.value);
        });
    }
    
    // 馈线时间范围选择器事件
    const feederTimeRangeSelector = document.getElementById('feeder-time-range');
    if (feederTimeRangeSelector) {
        feederTimeRangeSelector.addEventListener('change', function() {
            // 更新馈线页面图表数据
            const selectedFeeder = document.getElementById('feeder-selector').value;
            updateFeederChartsData(selectedFeeder, this.value);
        });
    }
    
    // 初始化加载默认页面的数据
    loadDefaultData();
    
    // 添加实时监控定时器
    setupRealTimeMonitoring();
});

// 加载默认数据
function loadDefaultData() {
    // 模拟从服务器获取数据
    // 在实际应用中，这里应该是一个AJAX请求
    console.log('加载默认数据...');
    
    // 找到当前激活的页面
    const activePageId = document.querySelector('.page.active').id;
    
    // 初始化所有图表
    setTimeout(function() {
        initAllCharts();
        
        // 额外处理当前页面的图表
        fixChartDisplay(activePageId);
        
        console.log('所有图表初始化完成');
    }, 300);
    
    // 设置数据刷新定时器
    setDataRefreshTimer();
}

// 更新图表数据
function updateChartsData(timeRange) {
    console.log(`更新图表数据，时间范围: ${timeRange}`);
    
    // 这里应该根据选择的时间范围发送AJAX请求获取新数据
    // 然后更新各个图表
    
    // 模拟数据更新
    switch(timeRange) {
        case 'day':
            // 更新为日数据
            updatePowerChartData(generateRandomData(24));
            updateEnergyConsumptionData(generateRandomData(24));
            break;
        case 'week':
            // 更新为周数据
            updatePowerChartData(generateRandomData(7));
            updateEnergyConsumptionData(generateRandomData(7));
            break;
        case 'month':
            // 更新为月数据
            updatePowerChartData(generateRandomData(30));
            updateEnergyConsumptionData(generateRandomData(30));
            break;
        case 'year':
            // 更新为年数据
            updatePowerChartData(generateRandomData(12));
            updateEnergyConsumptionData(generateRandomData(12));
            break;
    }
}

// 设置数据刷新定时器
function setDataRefreshTimer() {
    // 每30秒刷新一次实时数据
    setInterval(() => {
        refreshRealTimeData();
    }, 30000);
}

// 刷新实时数据
function refreshRealTimeData() {
    console.log('刷新实时数据...');
    
    // 获取当前活动页面
    const activePage = document.querySelector('.page.active');
    if (!activePage) return;
    
    const pageId = activePage.id;
    
    // 更新实时功率图表
    updatePowerChartRealTimeData();
    
    // 更新功率因数仪表盘
    updatePowerFactorGauge();
    
    // 根据当前页面更新其他需要实时刷新的图表
    switch(pageId) {
        case 'overview':
            // 显示数据更新指示器
            showDataUpdateIndicator('power-chart');
            break;
        case 'transformer':
            // 更新变压器实时数据
            const transformerId = document.getElementById('transformer-selector')?.value || '1B';
            updateTransformerRealTimeData(transformerId);
            // 显示数据更新指示器
            showDataUpdateIndicator('transformer-power');
            break;
        case 'feeder':
            // 更新馈线实时数据
            const feederId = document.getElementById('feeder-selector')?.value || '212';
            updateFeederRealTimeData(feederId);
            // 显示数据更新指示器
            showDataUpdateIndicator('feeder-current');
            break;
        case 'analysis':
            // 显示能耗分析页面数据更新指示器
            showDataUpdateIndicator('energy-trend');
            showDataUpdateIndicator('energy-anomaly');
            break;
    }
}

// 生成随机数据（用于演示）
function generateRandomData(count) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * 100) + 50);
    }
    return data;
}

// 更新功率图表数据
function updatePowerChartData(data) {
    if (window.powerChart) {
        // 更新图表数据
        window.powerChart.setOption({
            series: [{
                data: data
            }, {
                data: data.map(val => val * 0.8)
            }]
        });
    }
}

// 更新实时功率图表数据
function updatePowerChartRealTimeData() {
    if (window.powerChart) {
        const now = new Date();
        
        // 使用遥测数据代替随机数据
        const activePower = window.getTelemetryData('transformers', '1B_牵引变压器', 'activePower');
        const reactivePower = window.getTelemetryData('transformers', '1B_牵引变压器', 'reactivePower');
        
        // 添加新数据点
        window.powerChart.setOption({
            series: [{
                data: [...window.powerChart.getOption().series[0].data, {
                    name: now.toString(),
                    value: [now, activePower]
                }]
            }, {
                data: [...window.powerChart.getOption().series[1].data, {
                    name: now.toString(),
                    value: [now, reactivePower]
                }]
            }]
        });
        
        // 保持数据点数量在合理范围内（仅保留最近30点）
        if (window.powerChart.getOption().series[0].data.length > 30) {
            window.powerChart.setOption({
                series: [{
                    data: window.powerChart.getOption().series[0].data.slice(-30)
                }, {
                    data: window.powerChart.getOption().series[1].data.slice(-30)
                }]
            });
        }
    }
}

// 更新功率因数仪表盘
function updatePowerFactorGauge() {
    if (window.powerFactorGauge) {
        // 从1B牵引变压器获取功率因数数据
        const powerFactorValue = window.getTelemetryData('transformers', '1B_牵引变压器', 'powerFactor');
        
        // 添加动画效果
        window.powerFactorGauge.setOption({
            series: [{
                data: [{
                    value: powerFactorValue,
                    name: '功率因数'
                }],
                animationDurationUpdate: 1000,
                animationEasingUpdate: 'bounceOut'
            }]
        });
        
        // 根据功率因数值调整颜色
        let gaugeColor = '#5d90ff';  // 默认蓝色
        
        if (powerFactorValue < 0.90) {
            gaugeColor = '#ff9800';  // 橙色警告
        }
        if (powerFactorValue < 0.85) {
            gaugeColor = '#f44336';  // 红色警告
        }
        
        // 更新仪表盘颜色
        window.powerFactorGauge.setOption({
            series: [{
                axisLine: {
                    lineStyle: {
                        color: [
                            [0.85, '#f44336'],  // 红色区域
                            [0.90, '#ff9800'],  // 橙色区域
                            [1, '#4caf50']      // 绿色区域
                        ]
                    }
                },
                pointer: {
                    itemStyle: {
                        color: gaugeColor
                    }
                },
                detail: {
                    color: gaugeColor
                }
            }]
        });
    }
}

// 更新能耗消耗图表数据
function updateEnergyConsumptionData(data) {
    if (window.energyConsumptionChart) {
        window.energyConsumptionChart.setOption({
            series: [{
                data: data
            }, {
                data: data.map(val => val * 0.6)
            }]
        });
    }
}

// 设置实时监控
function setupRealTimeMonitoring() {
    // 每隔30秒更新一次实时数据
    setInterval(function() {
        // 更新活动页面的实时数据
        const activePage = document.querySelector('.page.active');
        if (!activePage) return;
        
        const pageId = activePage.id;
        
        switch(pageId) {
            case 'overview':
                updateOverviewRealTimeData();
                break;
            case 'transformer':
                updateTransformerRealTimeData();
                break;
            case 'feeder':
                updateFeederRealTimeData();
                break;
            case 'analysis':
                // 分析页面不需要实时更新
                break;
        }
    }, 30000); // 30秒刷新一次
}

// 更新总览页面实时数据
function updateOverviewRealTimeData() {
    // 更新实时功率监控数据
    if (window.powerChart) {
        const now = new Date();
        const value = Math.random() * 200 + 300;
        const reactiveValue = value * 0.7;
        
        const data = {
            name: now.toString(),
            value: [now, value]
        };
        
        const reactiveData = {
            name: now.toString(),
            value: [now, reactiveValue]
        };
        
        const option = window.powerChart.getOption();
        
        // 添加新数据点
        option.series[0].data.push(data);
        option.series[1].data.push(reactiveData);
        
        // 保留最近30个数据点
        if (option.series[0].data.length > 30) {
            option.series[0].data.shift();
            option.series[1].data.shift();
        }
        
        window.powerChart.setOption(option);
    }
    
    // 更新功率因数仪表盘
    if (window.powerFactorGauge) {
        const option = window.powerFactorGauge.getOption();
        const newValue = (Math.random() * 0.1 + 0.87).toFixed(2);
        option.series[0].data[0].value = newValue;
        window.powerFactorGauge.setOption(option);
    }
}

// 更新变压器页面实时数据
function updateTransformerRealTimeData() {
    // 模拟实时更新变压器温度
    if (window.transformerTempChart) {
        const selectedTransformer = document.getElementById('transformer-temp-selector').value;
        const oilTempElement = document.getElementById('transformer-oil-temp');
        const copperTempElement = document.getElementById('transformer-copper-temp');
        
        if (oilTempElement && copperTempElement) {
            // 随机浮动温度值
            const currentOilTemp = parseFloat(oilTempElement.textContent);
            const currentCopperTemp = parseFloat(copperTempElement.textContent);
            
            const newOilTemp = (currentOilTemp + (Math.random() * 2 - 1)).toFixed(1);
            const newCopperTemp = (currentCopperTemp + (Math.random() * 2 - 1)).toFixed(1);
            
            oilTempElement.textContent = newOilTemp + ' ℃';
            copperTempElement.textContent = newCopperTemp + ' ℃';
        }
    }
    
    // 更新自耦变压器温度
    if (window.autotransformerTempChart) {
        const at1TempElement = document.getElementById('autotransformer-temp-1');
        const at2TempElement = document.getElementById('autotransformer-temp-2');
        const at3TempElement = document.getElementById('autotransformer-temp-3');
        const at4TempElement = document.getElementById('autotransformer-temp-4');
        
        if (at1TempElement && at2TempElement && at3TempElement && at4TempElement) {
            // 随机浮动温度值
            at1TempElement.textContent = (parseFloat(at1TempElement.textContent) + (Math.random() * 1.5 - 0.7)).toFixed(1) + ' ℃';
            at2TempElement.textContent = (parseFloat(at2TempElement.textContent) + (Math.random() * 1.5 - 0.7)).toFixed(1) + ' ℃';
            at3TempElement.textContent = (parseFloat(at3TempElement.textContent) + (Math.random() * 1.5 - 0.7)).toFixed(1) + ' ℃';
            at4TempElement.textContent = (parseFloat(at4TempElement.textContent) + (Math.random() * 1.5 - 0.7)).toFixed(1) + ' ℃';
        }
    }
}

// 更新馈线页面实时数据
function updateFeederRealTimeData() {
    // 更新馈线表格数据
    const selectedFeeder = document.getElementById('feeder-selector').value;
    if (selectedFeeder !== 'all') {
        updateFeederTableData(selectedFeeder); // 更新单条馈线的详细数据
    } else {
        updateFeederTableData('all'); // 更新所有馈线的概要数据
    }
}

// 设备页面已移除
