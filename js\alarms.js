// 告警表格更新功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化告警数据
    initAlarmTable();
    
    // 每隔60秒更新一次告警数据
    setInterval(updateAlarmTable, 60000);
});

// 初始化告警表格
function initAlarmTable() {
    const alarmTableBody = document.querySelector('table.data-table tbody');
    if (!alarmTableBody) return;
    
    // 获取告警数据
    const alarmData = window.getDeviceAlarms(5);
    
    // 清空现有表格内容
    alarmTableBody.innerHTML = '';
    
    // 填充告警数据
    populateAlarmTable(alarmTableBody, alarmData);
}

// 更新告警表格
function updateAlarmTable() {
    const alarmTableBody = document.querySelector('table.data-table tbody');
    if (!alarmTableBody) return;
    
    // 获取新的告警数据（前3条）
    const newAlarmData = window.getDeviceAlarms(3);
    
    // 清空表格后重新填充
    alarmTableBody.innerHTML = '';
    populateAlarmTable(alarmTableBody, newAlarmData);
    
    // 显示更新提示
    const table = alarmTableBody.closest('.data-table');
    if (table) {
        table.classList.add('flash-update');
        setTimeout(() => {
            table.classList.remove('flash-update');
        }, 1000);
    }
}

// 填充告警表格
function populateAlarmTable(tableBody, alarmData) {
    alarmData.forEach(alarm => {
        const row = document.createElement('tr');
        
        // 设置行类型样式（告警等级）
        switch(alarm.severity) {
            case 'critical':
                row.classList.add('critical');
                break;
            case 'warning':
                row.classList.add('warning');
                break;
            case 'normal':
                row.classList.add('normal');
                break;
        }
        
        // 填充单元格数据
        row.innerHTML = `
            <td>${alarm.time}</td>
            <td>${alarm.device}</td>
            <td>${alarm.type}</td>
            <td>${alarm.value}</td>
            <td>${alarm.threshold}</td>
            <td>${alarm.status}</td>
        `;
        
        tableBody.appendChild(row);
    });
}
