// 图表主题和样式优化
(function() {
    // 注册一个适合深色背景的自定义主题
    echarts.registerTheme('darkBlue', {
        // 调色板 - 为图表元素提供更高对比度的颜色
        color: [
            '#36a3ff', '#ffcc33', '#4cd2a0', '#fd7d50', '#a183e0',
            '#65dbe9', '#ffb571', '#5e96db', '#ff7ebf', '#b2e269'
        ],
        
        // 背景色 - 透明以使用容器背景色
        backgroundColor: 'transparent',
        
        // 全局文字颜色
        textStyle: {
            color: '#c0d0ff'
        },
        
        // 标题
        title: {
            textStyle: {
                color: '#5d90ff',
                fontWeight: 600
            },
            subtextStyle: {
                color: '#a0b4e6'
            }
        },
        
        // 图例
        legend: {
            textStyle: {
                color: '#c0d0ff'
            }
        },
        
        // 网格
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        
        // 线条样式
        line: {
            lineStyle: {
                width: 3
            },
            symbolSize: 7,
            symbol: 'circle',
            smooth: true
        },
        
        // 柱状图样式
        bar: {
            itemStyle: {
                barBorderWidth: 0,
                barBorderRadius: 4
            }
        },
        
        // 饼图样式
        pie: {
            itemStyle: {
                borderWidth: 1,
                borderColor: '#0e1538'
            }
        },
        
        // 散点图样式
        scatter: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#0e1538'
            }
        },
        
        // 盒须图样式
        boxplot: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#0e1538'
            }
        },
        
        // 平行坐标系
        parallel: {
            lineStyle: {
                width: 2
            }
        },
        
        // 雷达图
        radar: {
            axisLine: {
                lineStyle: {
                    color: '#26355c'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#26355c'
                }
            },
            splitArea: {
                areaStyle: {
                    color: ['rgba(14, 21, 56, 0.3)', 'rgba(14, 21, 56, 0.5)']
                }
            }
        },
        
        // 坐标轴相关
        categoryAxis: {
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#26355c',
                    type: 'dashed'
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#c0d0ff'
            }
        },
        
        // 数值轴
        valueAxis: {
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#26355c',
                    type: 'dashed'
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#c0d0ff'
            }
        },
        
        // 时间坐标轴
        timeAxis: {
            axisLine: {
                lineStyle: {
                    color: '#4c5c84'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#26355c'
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#c0d0ff'
            }
        },
        
        // 图例滚动
        dataZoom: {
            textStyle: {
                color: '#c0d0ff'
            },
            borderColor: '#26355c',
            backgroundColor: 'rgba(14, 21, 56, 0.7)',
            fillerColor: 'rgba(93, 144, 255, 0.2)',
            handleStyle: {
                color: '#5d90ff',
                borderColor: '#5d90ff'
            },
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            dataBackground: {
                lineStyle: {
                    color: '#5d90ff'
                },
                areaStyle: {
                    color: '#5d90ff'
                }
            }
        },
        
        // 视觉映射
        visualMap: {
            textStyle: {
                color: '#c0d0ff'
            }
        },
        
        // 时间线
        timeline: {
            lineStyle: {
                color: '#5d90ff',
                width: 1
            },
            itemStyle: {
                color: '#5d90ff',
                borderWidth: 1
            },
            controlStyle: {
                color: '#5d90ff',
                borderColor: '#5d90ff',
                borderWidth: 0.5
            },
            checkpointStyle: {
                color: '#5d90ff',
                borderColor: '#ffffff'
            },
            label: {
                color: '#c0d0ff'
            },
            emphasis: {
                itemStyle: {
                    color: '#5d90ff'
                },
                controlStyle: {
                    color: '#5d90ff',
                    borderColor: '#5d90ff',
                    borderWidth: 0.5
                },
                label: {
                    color: '#c0d0ff'
                }
            }
        },
        
        // 提示框
        tooltip: {
            backgroundColor: 'rgba(14, 21, 56, 0.9)',
            borderColor: '#3a4a8e',
            borderWidth: 1,
            padding: [8, 12],
            textStyle: {
                color: '#ffffff',
                fontSize: 13
            },
            axisPointer: {
                lineStyle: {
                    color: '#5d90ff',
                    width: 1
                },
                crossStyle: {
                    color: '#5d90ff',
                    width: 1
                },
                shadowStyle: {
                    color: 'rgba(93, 144, 255, 0.1)'
                }
            }
        }
    });

    // 初始化前的全局函数 - 在每个图表初始化前应用
    window.applyChartTheme = function(chartInstance) {
        // 确保图表实例存在
        if (!chartInstance) return;
        
        // 销毁旧的实例
        chartInstance.dispose();
        
        // 获取容器元素
        const chartDom = chartInstance.getDom();
        
        // 使用新主题重新初始化
        return echarts.init(chartDom, 'darkBlue');
    };
    
    // 图表初始化前的全局处理
    const originalEchartsInit = echarts.init;
    echarts.init = function(dom, theme, opts) {
        // 如果没有指定主题，使用自定义的darkBlue主题
        theme = theme || 'darkBlue';
        return originalEchartsInit.call(this, dom, theme, opts);
    };
})();
