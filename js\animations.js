// 页面动画和过渡效果
document.addEventListener('DOMContentLoaded', function() {
    // 增强页面切换效果
    enhancePageTransitions();
    
    // 添加卡片和图表载入动画
    enhanceCardAnimations();
});

// 增强页面切换效果
function enhancePageTransitions() {
    // 为所有导航链接添加增强的点击处理
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        // 移除可能已存在的监听器（避免重复添加）
        const originalClickEvent = link.onclick;
        link.onclick = null;
        
        // 添加增强的点击处理
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const pageId = this.getAttribute('data-page');
            const currentActivePage = document.querySelector('.page.active');
            
            // 如果点击的是当前活动页面，不执行任何操作
            if (currentActivePage && currentActivePage.id === pageId) {
                return;
            }
            
            // 添加页面切换的加载指示器
            document.querySelector('header').classList.add('loading');
            
            // 更新导航栏状态
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // 添加淡出效果到当前页面
            if (currentActivePage) {
                currentActivePage.style.opacity = '0';
                currentActivePage.style.transform = 'translateY(-20px)';
            }
            
            // 延迟切换页面以允许动画效果完成
            setTimeout(() => {
                // 先隐藏所有页面
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });
                
                // 显示目标页面
                const targetPage = document.getElementById(pageId);
                targetPage.classList.add('active');
                targetPage.style.opacity = '0';
                targetPage.style.transform = 'translateY(20px)';
                
                // 强制重绘以确保CSS动画正确应用
                void targetPage.offsetWidth;
                
                // 添加载入动画
                targetPage.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                targetPage.style.opacity = '1';
                targetPage.style.transform = 'translateY(0)';
                
                // 修复目标页面的图表
                fixChartDisplay(pageId);
                
                // 移除加载指示器
                setTimeout(() => {
                    document.querySelector('header').classList.remove('loading');
                }, 300);
                
                // 如果原本有点击事件处理函数，也调用它
                if (originalClickEvent) {
                    originalClickEvent.call(this, e);
                }
            }, 300);
        });
    });
}

// 增强卡片载入动画
function enhanceCardAnimations() {
    // 为所有卡片添加延迟载入动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        // 初始状态
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        // 延迟添加动画类
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + index * 100); // 每个卡片的延迟都不同
    });
}
