/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #060a21;
    color: #ffffff;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
header {
    background-color: #0e1538;
    color: #e6e6e6;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid #1a2357;
}

header h1 {
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #5d90ff;
    text-shadow: 0 0 10px rgba(93, 144, 255, 0.5);
}

nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
}

nav ul li {
    margin: 0 10px;
}

nav ul li a {
    color: #c0d0ff;
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 4px;
    transition: all 0.3s;
    font-weight: 500;
}

nav ul li a:hover {
    background-color: rgba(77, 128, 228, 0.2);
    color: #ffffff;
}

nav ul li a.active {
    background-color: rgba(77, 128, 228, 0.3);
    color: #ffffff;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(77, 128, 228, 0.3);
}

/* 主内容区域 */
main {
    padding: 20px 0;
}

.page {
    display: none;
}

.page.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #1a2357;
}

.dashboard-header h2 {
    color: #5d90ff;
    font-size: 22px;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(93, 144, 255, 0.5);
    letter-spacing: 0.5px;
}

.time-selector {
    display: flex;
    align-items: center;
}

.time-selector span {
    color: #a0b4e6;
}

.time-selector select {
    margin-left: 10px;
    padding: 8px 15px;
    border: 1px solid #1a2357;
    border-radius: 4px;
    background-color: #0e1538;
    color: #e6e6e6;
    box-shadow: 0 0 5px rgba(77, 128, 228, 0.2);
    cursor: pointer;
    transition: all 0.3s;
}

.time-selector select:hover {
    border-color: #4d80e4;
    box-shadow: 0 0 10px rgba(77, 128, 228, 0.4);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.dashboard-row {
    margin-bottom: 20px;
}

.card {
    background-color: #0e1538;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    border: 1px solid #1a2357;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(77, 128, 228, 0.25);
}

.card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #1a2357;
    background-color: #0a102e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #5d90ff;
    text-shadow: 0 0 5px rgba(93, 144, 255, 0.5);
    letter-spacing: 0.5px;
}

.device-selector {
    position: absolute;
    right: 15px;
    top: 15px;
}

.device-selector select {
    background-color: rgba(14, 21, 56, 0.7);
    border: 1px solid #2a3a7e;
    color: #c0d0ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 10px 15px;
    border-top: 1px solid rgba(26, 35, 87, 0.5);
    background-color: rgba(14, 21, 56, 0.4);
}

.data-metrics {
    display: flex;
    justify-content: space-around;
}

.metric {
    text-align: center;
}

.metric .label {
    color: #c0d0ff;
    font-size: 13px;
    margin-bottom: 5px;
    font-weight: 500;
}

.metric .value {
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
}

.chart {
    width: 100%;
    height: 300px;
    border-radius: 4px;
    padding: 10px;
    background-color: rgba(14, 21, 56, 0.5);
    position: relative;
    overflow: hidden;
    min-height: 300px;
    box-shadow: inset 0 0 15px rgba(93, 144, 255, 0.15);
    border: 1px solid rgba(93, 144, 255, 0.2);
}

/* 为ECharts图表添加加载指示器 */
.chart::before {
    content: "加载中...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #5d90ff;
    font-size: 16px;
    font-weight: 500;
    opacity: 0.8;
    z-index: -1;
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #1a2357;
    border-radius: 8px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #1a2357;
}

.data-table th {
    background-color: #0a102e;
    font-weight: 600;
    color: #5d90ff;
    text-shadow: 0 0 5px rgba(93, 144, 255, 0.5);
    letter-spacing: 0.5px;
}

.data-table td {
    color: #f0f5ff;
    font-weight: 500;
}

.data-table tr:hover {
    background-color: #111a42;
}

.data-table tr.warning {
    background-color: rgba(255, 152, 0, 0.15);
    border-left: 3px solid #ff9800;
}

.data-table tr.critical {
    background-color: rgba(244, 67, 54, 0.15);
    border-left: 3px solid #f44336;
}

.data-table tr.normal {
    background-color: rgba(76, 175, 80, 0.15);
    border-left: 3px solid #4caf50;
}

/* 页脚样式 */
footer {
    text-align: center;
    padding: 20px 0;
    color: #666;
    border-top: 1px solid #1a2357;
    margin-top: 20px;
}

/* 添加数据指标卡样式 */
.metric-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.metric-card {
    background-color: #0e1538;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid #1a2357;
    transition: transform 0.3s, box-shadow 0.3s;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(77, 128, 228, 0.25);
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
    color: #4d80e4;
}

.metric-label {
    font-size: 14px;
    color: #a0b4e6;
}

.metric-change {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 12px;
    margin-top: 5px;
}

.metric-change.positive {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.metric-change.negative {
    background-color: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

/* 馈线筛选器样式 */
.feeder-filter {
    display: flex;
    margin-bottom: 20px;
    padding: 15px;
    background-color: rgba(14, 21, 56, 0.4);
    border-radius: 5px;
    border: 1px solid #1a2357;
}

.filter-group {
    margin-right: 20px;
    display: flex;
    align-items: center;
}

.filter-group label {
    margin-right: 8px;
    color: #a0b4e6;
    font-size: 14px;
}

.filter-group select {
    background-color: rgba(14, 21, 56, 0.7);
    border: 1px solid #2a3a7e;
    color: #a0b4e6;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    nav ul li {
        margin: 5px 0;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .time-selector {
        margin-top: 10px;
    }
}

/* 确保所有选择器有更高对比度 */
select {
    color: #ffffff !important;
    background-color: rgba(14, 21, 56, 0.85) !important;
    border: 1px solid #3a4a8e !important;
}

/* 响应式布局改进 */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    nav ul {
        flex-wrap: wrap;
    }
    
    nav ul li {
        margin: 5px;
    }
    
    .card-body {
        padding: 10px;
    }
    
    .chart {
        height: 250px;
        min-height: 250px;
    }
    
    .data-metrics {
        flex-direction: column;
    }
    
    .metric {
        margin-bottom: 10px;
    }
}

/* 打印样式 */
@media print {
    body {
        background-color: #ffffff;
        color: #000000;
    }
    
    .container {
        max-width: 100%;
    }
    
    nav, .dashboard-header .time-selector {
        display: none;
    }
    
    .card {
        break-inside: avoid;
        page-break-inside: avoid;
        margin-bottom: 20px;
        box-shadow: none;
        border: 1px solid #cccccc;
    }
    
    .chart {
        height: 300px;
        border: none;
        box-shadow: none;
    }
}

/* 暗色模式优化 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #060a21;
    }
    
    /* 其他暗色模式下的样式已经在主样式表中定义 */
}
